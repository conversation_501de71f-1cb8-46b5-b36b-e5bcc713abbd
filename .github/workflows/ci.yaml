name: ci
on:
    push:
        branches:
            - main
    pull_request:
        branches:
            - main

jobs:
    test:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - uses: actions/setup-node@v4
            - uses: bahmutov/npm-install@v1
            - run: npm run build-keycloak-theme

    check_if_version_upgraded:
        name: Check if version upgrade
        if: github.event_name == 'push'
        runs-on: ubuntu-latest
        needs: test
        outputs:
            from_version: ${{ steps.step1.outputs.from_version }}
            to_version: ${{ steps.step1.outputs.to_version }}
            is_upgraded_version: ${{ steps.step1.outputs.is_upgraded_version }}
            is_pre_release: ${{steps.step1.outputs.is_pre_release }}
        steps:
            - uses: garronej/ts-ci@v2.1.5
              id: step1
              with:
                  action_name: is_package_json_version_upgraded
                  branch: ${{ github.head_ref || github.ref }}

    create_github_release:
        runs-on: ubuntu-latest
        needs: check_if_version_upgraded
        if: needs.check_if_version_upgraded.outputs.is_upgraded_version == 'true'
        steps:
            - uses: actions/checkout@v4
            - uses: actions/setup-node@v4
            - uses: bahmutov/npm-install@v1
            - run: npm run build-keycloak-theme
            - uses: softprops/action-gh-release@v2
              with:
                  name: Release v${{ needs.check_if_version_upgraded.outputs.to_version }}
                  tag_name: v${{ needs.check_if_version_upgraded.outputs.to_version }}
                  target_commitish: ${{ github.head_ref || github.ref }}
                  generate_release_notes: true
                  draft: false
                  prerelease: ${{ needs.check_if_version_upgraded.outputs.is_pre_release == 'true' }}
                  files: dist_keycloak/keycloak-theme-*.jar
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
