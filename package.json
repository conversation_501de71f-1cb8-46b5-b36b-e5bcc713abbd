{"name": "<PERSON><PERSON><PERSON>akify-starter", "version": "0.0.0", "description": "Starter for Keycloakify 11", "repository": {"type": "git", "url": "git://github.com/codegouvfr/keycloakify-starter.git"}, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build-keycloak-theme": "npm run build && keycloakify build", "storybook": "storybook dev -p 6006", "format": "prettier . --write"}, "license": "MIT", "keywords": [], "dependencies": {"keycloakify": "^11.8.32", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "storybook": "^8.1.10", "@storybook/react": "^8.1.10", "@storybook/react-vite": "^8.1.10", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-storybook": "^0.11.1", "globals": "^15.12.0", "prettier": "3.3.1", "typescript": "^5.2.2", "typescript-eslint": "^8.15.0", "vite": "^5.0.8"}, "engines": {"node": "^18.0.0 || >=20.0.0"}}