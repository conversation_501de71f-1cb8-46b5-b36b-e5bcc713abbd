# Assets Directory

This directory contains static assets that are imported and processed by Vite.

## Usage

### Images
Place your images in the `images/` subdirectory and import them in your components:

```jsx
import myImage from '../assets/images/my-image.png';

function MyComponent() {
  return <img src={myImage} alt="Description" />;
}
```

### Benefits of using src/assets/
- Images are processed and optimized by Vite
- Automatic cache busting with hashed filenames
- TypeScript support for imports
- Better tree shaking and bundling

## Alternative: Static Directory
For images that don't need processing, you can also place them in `/static/images/` and reference them directly:

```jsx
function MyComponent() {
  return <img src="/images/my-image.png" alt="Description" />;
}
```
