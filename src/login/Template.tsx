import { useEffect } from "react";
import { clsx } from "keycloakify/tools/clsx";
import { kcSanitize } from "keycloakify/lib/kcSanitize";
import type { TemplateProps } from "keycloakify/login/TemplateProps";
import { useSetClassName } from "keycloakify/tools/useSetClassName";
import { useInitialize } from "keycloakify/login/Template.useInitialize";
import type { I18n } from "./i18n";
import type { KcContext } from "./KcContext";

export default function Template(props: TemplateProps<KcContext, I18n>) {
    const {
        displayInfo = false,
        displayMessage = true,
        displayRequiredFields = false,
        headerNode,
        socialProvidersNode = null,
        infoNode = null,
        documentTitle,
        kcContext,
        i18n,
        doUseDefaultCss,
        children
    } = props;

    const { msg, msgStr, currentLanguage, enabledLanguages } = i18n;

    const { realm, auth, url, message, isAppInitiatedAction } = kcContext;

    useEffect(() => {
        document.title = documentTitle ?? msgStr("loginTitle", realm.displayName);
    }, []);

    useSetClassName({
        qualifiedName: "html",
        className: "h-full"
    });

    useSetClassName({
        qualifiedName: "body",
        className: "h-full bg-[#e4e7ef] m-0 p-0"
    });

    const { isReadyToRender } = useInitialize({ kcContext, doUseDefaultCss });

    if (!isReadyToRender) {
        return null;
    }

    return (
        <div className="relative w-full h-screen bg-[#e4e7ef] flex">
            {/* Left side with blue gradient background and HighGround logo */}
            <div className="w-1/2 h-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center relative overflow-hidden">
                {/* Flowing wave background pattern */}
                <div className="absolute inset-0">
                    <svg className="absolute bottom-0 w-full h-full" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
                        {/* Flowing wave patterns */}
                        <path d="M0 300 Q128 250 256 300 T512 300 V512 H0 Z" fill="rgba(255,255,255,0.1)"/>
                        <path d="M0 350 Q128 300 256 350 T512 350 V512 H0 Z" fill="rgba(255,255,255,0.08)"/>
                        <path d="M0 400 Q128 350 256 400 T512 400 V512 H0 Z" fill="rgba(255,255,255,0.06)"/>
                        <path d="M0 450 Q128 400 256 450 T512 450 V512 H0 Z" fill="rgba(255,255,255,0.04)"/>
                    </svg>
                </div>

                {/* HighGround Logo */}
                <div className="relative z-10 text-white text-center">
                    <div className="flex flex-col items-center">
                        {/* Logo Icon */}
                        <div className="mb-4">
                            <svg width="120" height="60" viewBox="0 0 120 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                                {/* HighGround geometric logo */}
                                <g fill="white">
                                    <path d="M20 35 L35 20 L50 30 L65 15 L80 25 L95 10 L110 20 V45 H20 Z" opacity="0.9"/>
                                    <path d="M15 40 L30 25 L45 35 L60 20 L75 30 L90 15 L105 25 V50 H15 Z" opacity="0.7"/>
                                    <path d="M10 45 L25 30 L40 40 L55 25 L70 35 L85 20 L100 30 V55 H10 Z" opacity="0.5"/>
                                </g>
                            </svg>
                        </div>
                        {/* Company Name */}
                        <h1 className="text-4xl font-bold tracking-wide">HighGround</h1>
                    </div>
                </div>
            </div>

            {/* Right side with login form */}
            <div className="w-1/2 flex items-center justify-center p-8">
                <div className="bg-white rounded-lg shadow-lg p-8 w-full max-w-md">
                    {/* Language selector */}
                    {enabledLanguages.length > 1 && (
                        <div className="absolute top-4 right-4">
                            <div className="relative">
                                <button
                                    tabIndex={1}
                                    id="kc-current-locale-link"
                                    aria-label={msgStr("languages")}
                                    aria-haspopup="true"
                                    aria-expanded="false"
                                    aria-controls="language-switch1"
                                    className="text-sm text-gray-600 hover:text-gray-800"
                                >
                                    {currentLanguage.label}
                                </button>
                                <ul
                                    role="menu"
                                    tabIndex={-1}
                                    aria-labelledby="kc-current-locale-link"
                                    aria-activedescendant=""
                                    id="language-switch1"
                                    className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden"
                                >
                                    {enabledLanguages.map(({ languageTag, label, href }, i) => (
                                        <li key={languageTag} role="none">
                                            <a
                                                role="menuitem"
                                                id={`language-${i + 1}`}
                                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                href={href}
                                            >
                                                {label}
                                            </a>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    )}

                    {/* Header */}
                    <div className="text-center mb-8">
                        {(() => {
                            const node = !(auth !== undefined && auth.showUsername && !auth.showResetCredentials) ? (
                                <h1 className="text-3xl font-semibold text-[#3769da] mb-2">{headerNode}</h1>
                            ) : (
                                <div className="mb-4">
                                    <label className="text-lg font-medium text-gray-900">{auth.attemptedUsername}</label>
                                    <a
                                        href={url.loginRestartFlowUrl}
                                        aria-label={msgStr("restartLoginTooltip")}
                                        className="ml-2 text-blue-600 hover:text-blue-800"
                                    >
                                        <span className="text-sm">{msg("restartLoginTooltip")}</span>
                                    </a>
                                </div>
                            );

                            if (displayRequiredFields) {
                                return (
                                    <div>
                                        <div className="text-sm text-gray-600 mb-4">
                                            <span className="text-red-500">*</span>
                                            {msg("requiredFields")}
                                        </div>
                                        {node}
                                    </div>
                                );
                            }

                            return node;
                        })()}
                        <p className="text-sm text-gray-600">Sign into your account</p>
                    </div>

                    {/* Messages */}
                    {displayMessage && message !== undefined && (message.type !== "warning" || !isAppInitiatedAction) && (
                        <div
                            className={clsx(
                                "mb-4 p-4 rounded-md",
                                message.type === "success" && "bg-green-50 text-green-800 border border-green-200",
                                message.type === "warning" && "bg-yellow-50 text-yellow-800 border border-yellow-200",
                                message.type === "error" && "bg-red-50 text-red-800 border border-red-200",
                                message.type === "info" && "bg-blue-50 text-blue-800 border border-blue-200"
                            )}
                        >
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    {message.type === "success" && <span>✓</span>}
                                    {message.type === "warning" && <span>⚠</span>}
                                    {message.type === "error" && <span>✕</span>}
                                    {message.type === "info" && <span>ℹ</span>}
                                </div>
                                <div className="ml-3">
                                    <span dangerouslySetInnerHTML={{ __html: kcSanitize(message.summary) }} />
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Form content */}
                    <div className="space-y-6">
                        {children}

                        {auth !== undefined && auth.showTryAnotherWayLink && (
                            <form id="kc-select-try-another-way-form" action={url.loginAction} method="post">
                                <div>
                                    <input type="hidden" name="tryAnotherWay" value="on" />
                                    <a
                                        href="#"
                                        id="try-another-way"
                                        onClick={() => {
                                            document.forms["kc-select-try-another-way-form" as never].submit();
                                            return false;
                                        }}
                                        className="text-blue-600 hover:text-blue-800 text-sm"
                                    >
                                        {msg("doTryAnotherWay")}
                                    </a>
                                </div>
                            </form>
                        )}

                        {socialProvidersNode}

                        {displayInfo && (
                            <div className="mt-6 text-center">
                                {infoNode}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
