import { useEffect } from "react";
import { clsx } from "keycloakify/tools/clsx";
import { kcSanitize } from "keycloakify/lib/kcSanitize";
import type { TemplateProps } from "keycloakify/login/TemplateProps";
import { useSetClassName } from "keycloakify/tools/useSetClassName";
import { useInitialize } from "keycloakify/login/Template.useInitialize";
import type { I18n } from "./i18n";
import type { KcContext } from "./KcContext";

export default function Template(props: TemplateProps<KcContext, I18n>) {
    const {
        displayInfo = false,
        displayMessage = true,
        displayRequiredFields = false,
        headerNode,
        socialProvidersNode = null,
        infoNode = null,
        documentTitle,
        kcContext,
        i18n,
        doUseDefaultCss,
        children
    } = props;

    const { msg, msgStr, currentLanguage, enabledLanguages } = i18n;

    const { realm, auth, url, message, isAppInitiatedAction } = kcContext;

    useEffect(() => {
        document.title = documentTitle ?? msgStr("loginTitle", realm.displayName);
    }, []);

    useSetClassName({
        qualifiedName: "html",
        className: "h-full"
    });

    useSetClassName({
        qualifiedName: "body",
        className: "h-full bg-[#e4e7ef] m-0 p-0"
    });

    const { isReadyToRender } = useInitialize({ kcContext, doUseDefaultCss });

    if (!isReadyToRender) {
        return null;
    }

    return (
        <div className="relative w-full h-screen bg-[#e4e7ef] flex">
            {/* Left side with blue gradient background and HighGround logo */}
            <div className="w-1/2 h-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center">
                <div className="text-white text-center">
                    {/* HighGround Logo */}
                    <div className="mb-8">
                        <svg width="200" height="80" viewBox="0 0 200 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g fill="white">
                                {/* Mountain/Wave design representing HighGround */}
                                <path d="M20 50 L40 30 L60 40 L80 20 L100 35 L120 25 L140 45 L160 30 L180 40 L200 25 V60 H20 Z" opacity="0.8"/>
                                <path d="M20 55 L50 35 L80 45 L110 30 L140 50 L170 35 L200 45 V65 H20 Z" opacity="0.6"/>
                                <path d="M20 60 L60 40 L100 50 L140 35 L180 45 L200 40 V70 H20 Z" opacity="0.4"/>
                            </g>
                            <text x="100" y="45" textAnchor="middle" fill="white" fontSize="24" fontWeight="600" fontFamily="Inter, sans-serif">
                                HighGround
                            </text>
                        </svg>
                    </div>
                </div>
            </div>

            {/* Right side with login form */}
            <div className="w-1/2 flex items-center justify-center p-8">
                <div className="bg-white rounded-lg shadow-lg p-8 w-full max-w-md">
                    {/* Language selector */}
                    {enabledLanguages.length > 1 && (
                        <div className="absolute top-4 right-4">
                            <div className="relative">
                                <button
                                    tabIndex={1}
                                    id="kc-current-locale-link"
                                    aria-label={msgStr("languages")}
                                    aria-haspopup="true"
                                    aria-expanded="false"
                                    aria-controls="language-switch1"
                                    className="text-sm text-gray-600 hover:text-gray-800"
                                >
                                    {currentLanguage.label}
                                </button>
                                <ul
                                    role="menu"
                                    tabIndex={-1}
                                    aria-labelledby="kc-current-locale-link"
                                    aria-activedescendant=""
                                    id="language-switch1"
                                    className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden"
                                >
                                    {enabledLanguages.map(({ languageTag, label, href }, i) => (
                                        <li key={languageTag} role="none">
                                            <a
                                                role="menuitem"
                                                id={`language-${i + 1}`}
                                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                href={href}
                                            >
                                                {label}
                                            </a>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    )}

                    {/* Header */}
                    <div className="text-center mb-8">
                        {(() => {
                            const node = !(auth !== undefined && auth.showUsername && !auth.showResetCredentials) ? (
                                <h1 className="text-3xl font-semibold text-[#3769da] mb-2">{headerNode}</h1>
                            ) : (
                                <div className="mb-4">
                                    <label className="text-lg font-medium text-gray-900">{auth.attemptedUsername}</label>
                                    <a
                                        href={url.loginRestartFlowUrl}
                                        aria-label={msgStr("restartLoginTooltip")}
                                        className="ml-2 text-blue-600 hover:text-blue-800"
                                    >
                                        <span className="text-sm">{msg("restartLoginTooltip")}</span>
                                    </a>
                                </div>
                            );

                            if (displayRequiredFields) {
                                return (
                                    <div>
                                        <div className="text-sm text-gray-600 mb-4">
                                            <span className="text-red-500">*</span>
                                            {msg("requiredFields")}
                                        </div>
                                        {node}
                                    </div>
                                );
                            }

                            return node;
                        })()}
                        <p className="text-sm text-gray-600">Sign into your account</p>
                    </div>

                    {/* Messages */}
                    {displayMessage && message !== undefined && (message.type !== "warning" || !isAppInitiatedAction) && (
                        <div
                            className={clsx(
                                "mb-4 p-4 rounded-md",
                                message.type === "success" && "bg-green-50 text-green-800 border border-green-200",
                                message.type === "warning" && "bg-yellow-50 text-yellow-800 border border-yellow-200",
                                message.type === "error" && "bg-red-50 text-red-800 border border-red-200",
                                message.type === "info" && "bg-blue-50 text-blue-800 border border-blue-200"
                            )}
                        >
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    {message.type === "success" && <span>✓</span>}
                                    {message.type === "warning" && <span>⚠</span>}
                                    {message.type === "error" && <span>✕</span>}
                                    {message.type === "info" && <span>ℹ</span>}
                                </div>
                                <div className="ml-3">
                                    <span dangerouslySetInnerHTML={{ __html: kcSanitize(message.summary) }} />
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Form content */}
                    <div className="space-y-6">
                        {children}

                        {auth !== undefined && auth.showTryAnotherWayLink && (
                            <form id="kc-select-try-another-way-form" action={url.loginAction} method="post">
                                <div>
                                    <input type="hidden" name="tryAnotherWay" value="on" />
                                    <a
                                        href="#"
                                        id="try-another-way"
                                        onClick={() => {
                                            document.forms["kc-select-try-another-way-form" as never].submit();
                                            return false;
                                        }}
                                        className="text-blue-600 hover:text-blue-800 text-sm"
                                    >
                                        {msg("doTryAnotherWay")}
                                    </a>
                                </div>
                            </form>
                        )}

                        {socialProvidersNode}

                        {displayInfo && (
                            <div className="mt-6 text-center">
                                {infoNode}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
