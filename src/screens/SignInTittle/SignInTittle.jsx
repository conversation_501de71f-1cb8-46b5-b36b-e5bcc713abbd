import { EyeIcon } from "lucide-react";
import React from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Card, CardContent } from "../../components/ui/card";
import { Input } from "../../components/ui/input";
import loginImage from "../../assets/images/loginImage.png";

export const SignInTittle = () => {
  // Data for the form fields
  const formData = {
    title: "Sign In",
    subtitle: "Sign into your account",
    emailLabel: "Email Address",
    emailPlaceholder: "<EMAIL>",
    passwordLabel: "Password",
    forgotPasswordText: "Forgot Password?",
    signInButtonText: "Sign In",
    orContinueWithText: "OR CONTINUE WITH",
    keycloakButtonText: "Login with Keycloak",
  };

  return (
    <div
      className="relative w-full h-screen bg-[#e4e7ef] flex"
      data-model-id="224:5414"
    >
      {/* Left side with image */}
      <div className="w-1/2 h-full">
        <img
          className="w-full h-full object-cover"
          alt="Login image"
          src={loginImage}
        />
      </div>

      {/* Right side with form */}
      <div className="w-1/2 flex items-center justify-center">
        <Card className="bg-white rounded-md p-6 w-[350px]">
          <CardContent className="p-0 flex flex-col gap-4">
            {/* Title */}
            <h2 className="w-full font-h2 font-[number:var(--h2-font-weight)] text-[#3769da] text-[length:var(--h2-font-size)] text-center tracking-[var(--h2-letter-spacing)] leading-[var(--h2-line-height)] [font-style:var(--h2-font-style)]">
              {formData.title}
            </h2>

            <div className="flex flex-col gap-4">
              {/* Subtitle */}
              <p className="w-full font-subtle font-[number:var(--subtle-font-weight)] text-slate-500 text-[length:var(--subtle-font-size)] text-center tracking-[var(--subtle-letter-spacing)] leading-[var(--subtle-line-height)] [font-style:var(--subtle-font-style)]">
                {formData.subtitle}
              </p>

              {/* Email field */}
              <div className="flex flex-col gap-1.5">
                <label className="font-medium text-[#0f172a] text-sm leading-5">
                  {formData.emailLabel}
                </label>
                <Input
                  className="h-[42px] bg-white border-slate-300 px-3 py-2"
                  defaultValue="<EMAIL>"
                />
              </div>

              {/* Password field */}
              <div className="flex flex-col gap-1.5">
                <label className="font-medium text-[#0f172a] text-sm leading-5">
                  {formData.passwordLabel}
                </label>
                <div className="relative">
                  <Input
                    type="password"
                    className="h-[42px] bg-white border-slate-300 px-3 py-2"
                    defaultValue="***********"
                  />
                  <EyeIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                </div>
                <a
                  href="#"
                  className="font-medium text-[#3769da] text-sm leading-5"
                >
                  {formData.forgotPasswordText}
                </a>
              </div>

              {/* Sign In button */}
              <Button className="w-full bg-[#3669da] text-white h-auto py-2">
                {formData.signInButtonText}
              </Button>
            </div>

            {/* Alternative login options */}
            <div className="flex flex-col gap-4">
              <p className="w-full font-medium text-slate-500 text-sm text-center">
                {formData.orContinueWithText}
              </p>
              <Button
                variant="outline"
                className="w-full border-[#3769da] text-[#3769da] h-auto py-2 flex gap-2"
              >
                <img
                  className="w-4 h-4 object-cover"
                  alt="Keycloak logo"
                  src="https://c.animaapp.com/8DwnSCSh/img/<EMAIL>"
                />
                {formData.keycloakButtonText}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
